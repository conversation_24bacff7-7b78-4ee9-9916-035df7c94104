# FastAPI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
httpx[http2]==0.25.2
python-multipart
python-dotenv

# Pydantic & typing
pydantic==2.5.0
pydantic-settings==2.1.0
pydantic_core==2.14.1
annotated-types

# Database
aiosqlite
SQLAlchemy
peewee

# LangChain & LLMs - COMPATIBLE VERSIONS (Fixed for your integration)
langchain==0.0.350
langchain-community==0.0.13
openai==0.28.1
tiktoken==0.5.2

# Keep Anthropic for your existing MCP functionality (separate from LangChain)
anthropic==0.21.0

# Core utilities
requests==2.31.0

# Optional CLI/dev experience
rich
typer
click

# HTML rendering & docs
markdown-it-py
mdurl
Pygments
colorama

# Flask-based sqlite viewer (optional)
sqlite-web

# Utilities
sniffio
shellingham

# Logging and web utils
h11
blinker
Werkzeug
itsdangerous
Jinja2
MarkupSafe
certifi
idna

# Google Sheets and Drive API dependencies
gspread==5.12.0
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
google-api-python-client==2.108.0
google-cloud-core==2.3.3
google-resumable-media==2.6.0
googleapis-common-protos==1.61.0
protobuf==4.25.1